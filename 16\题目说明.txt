题目16：卷积神经网络特征图可视化分析

【图片内容】：显示CNN三层卷积的特征图激活模式，包括原始输入图像（手写数字"8"）和各层的特征图可视化

【问题】：
观察图片中的特征图，根据代码中create_digit_8()函数的具体实现(上圆中心(9,14)半径5、下圆中心(18,14)半径5、连接区域img[12:16, 12:16])和CNN的卷积核大小(3×3)，以下关于Conv1-1和Conv1-3特征图对不同几何特征响应的分析哪个是正确的？

A. Conv1-1主要响应垂直边缘特征，在数字"8"的左右边界(列8和列20附近)激活最强；Conv1-3主要响应水平边缘，在上下边界激活最强
B. Conv1-1和Conv1-3都只对连接区域(12:16, 12:16)有响应，对圆形边缘完全不敏感
C. Conv1-1主要检测圆形弧线特征，在上下两个圆的弧线部分激活较强；Conv1-3主要检测角点特征，在连接区域的四个角点激活最强
D. Conv1-1对所有区域均匀响应，Conv1-3完全没有激活

【答案】：C

【推理过程】：
1）根据代码中create_digit_8()的实现，数字"8"由两个圆环(通过距离公式((i-center)**2 + (j-center)**2)生成)和一个矩形连接区域组成；
2）Conv1-1作为第一个卷积核，通过3×3的卷积操作能够检测到圆环的弧线特征，从图片可以观察到在上圆(中心(9,14))和下圆(中心(18,14))的弧线部分有明显激活；
3）Conv1-3卷积核学习到了不同的特征模式，主要对连接区域img[12:16, 12:16]的角点和边缘转折处敏感，这些几何特征在图片中表现为该区域四个角点的强激活响应。
