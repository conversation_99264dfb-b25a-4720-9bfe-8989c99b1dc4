题目18：GAN训练过程损失函数动态分析

【图片内容】：显示GAN训练过程中的四个关键指标图：生成器vs判别器损失曲线、判别器在真假数据上的准确率、损失比值变化、以及平滑后的损失趋势

【问题】：
观察图片中的判别器准确率曲线，根据代码中的真实数据生成函数generate_real_data()(两个高斯分布data1~N(1.0, 0.5²)和data2~N(-1.0, 0.5²))和BCE损失函数的0.5阈值判断机制，以下关于训练过程中判别器性能变化的分析哪个是正确的？

A. 判别器在真实数据上的准确率始终保持在0.9以上，在假数据上的准确率从0.9逐渐下降到0.6，说明生成器逐渐学会欺骗判别器
B. 判别器在真实数据和假数据上的准确率都从初始的0.5随机水平逐渐提升到0.8，然后保持稳定
C. 判别器在真实数据上的准确率保持稳定在0.8左右，在假数据上的准确率呈现先上升后下降的趋势，最终稳定在0.6左右
D. 判别器在真实数据和假数据上的准确率都呈现周期性震荡，没有明显的收敛趋势

【答案】：A

【推理过程】：
1）根据代码中discriminator的forward函数使用Sigmoid激活，结合BCE损失和0.5阈值判断(real_output > 0.5)，判别器需要学习区分真实数据的双峰高斯分布特征；
2）从图片的准确率曲线可以观察到，判别器在真实数据上的准确率(绿线)始终维持在较高水平(0.9以上)，说明真实数据的分布特征相对容易学习；
3）判别器在假数据上的准确率(橙线)从初始的高水平逐渐下降到约0.6，这正是生成器通过对抗训练逐渐学会生成更逼真数据的体现，符合GAN训练的理论预期。
